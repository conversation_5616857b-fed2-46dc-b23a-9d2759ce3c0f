# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Tools
Use the tool launcher for easy access to all components:
```cmd
run_tools.cmd
```

Individual tools can be run directly:
```bash
# Main client tool (CLI)
cd src && python client_tool.py --auth

# GUI client tool
cd src && python client_tool_gui.py

# Mini client tool (background monitoring)
cd src && python mini_client_tool.py

# Manifest generator
cd src && python manifest_generator.py

# Server automation
cd src && python server_automation.py

# Encryption tool
cd src && python encryption_tool.py
```

### Building Executables
```bash
# Build main tool
pyinstaller config/MTYB_SteamTool.spec

# Build mini tool
pyinstaller config/MTYB_MiniSteamTool.spec

# Build test tool
pyinstaller config/MTYB_Test.spec

# Build all (via tool launcher option 8)
run_tools.cmd
```

### Dependencies
Install required packages:
```bash
pip install -r config/requirements.txt
```

## Architecture Overview

This is a Steam game manifest management system with license-based authentication. The core architecture consists of:

### Core Components
- **Authentication Layer**: KeyAuth-based licensing system (`keyauth.py`)
- **Steam Integration**: Manifest downloading and Steam API interaction (`manifest_generator.py`)
- **Client Tools**: Multiple interfaces for different use cases
  - CLI tool (`client_tool.py`) - Main interactive interface
  - GUI tool (`client_tool_gui.py`) - Windows-based interface
  - Mini tool (`mini_client_tool.py`) - Background monitoring
- **Server Component**: Flask-based automation server (`server_automation.py`)
- **Security**: Encryption utilities and secure data handling (`encryption_tool.py`)

### Data Flow
1. License authentication through KeyAuth API
2. Steam path detection via Windows registry
3. Manifest downloading from Steam servers
4. Local caching in `depots/` directory with VDF configuration
5. Server automation for batch operations

### Key Files
- `secret.key`: Encryption key (keep secure)
- `config/requirements.txt`: Python dependencies
- `depots/*/config.vdf`: Steam depot configurations
- `cache/app.log`: Application logging

### Windows Integration
- Registry access for Steam path detection
- Windows security API integration
- Firewall and antivirus management
- OCR-based Steam activation detection using `assets/steam_activate_product.png`

## Project Structure
- `src/`: All Python source code
- `config/`: Configuration files and PyInstaller specs
- `scripts/`: PowerShell and batch automation scripts
- `assets/`: Icons and image templates
- `depots/`: Steam game manifest cache
- `cache/`: Temporary files and logs

## GUI Refactoring (New Modular Structure)
The original `client_tool_gui.py` (1296 lines) has been refactored into a modular structure:
- `src/gui/`: GUI components (tabs, themes, configuration)
- `src/gui_backend/`: Business logic (Steam, database, authentication)
- Use `client_tool_gui_refactored.py` for the new AI-friendly modular version

## Task Completion Protocol

**CRITICAL**: When you complete any task or reach a stopping point, you MUST send an email notification using the notification script before ending your response.

### Email Notification Command:
```bash
python3 /mnt/d/SourceCode/SteamManifestUpdater/notification_server.py "SUBJECT" "CONTENT"
```

### Required Information in Notifications:
1. **What task was completed** - Brief description of the work done
2. **Success/failure status** - Clear indication if task succeeded or failed
3. **Key results or next steps needed** - What was accomplished and what's next
4. **Any errors encountered** - Issues that occurred during execution

### Example Usage:
```bash
python3 notification_server.py "GUI Refactoring Complete" "Successfully refactored client_tool_gui.py from 1296 lines into modular structure. Created 16 focused files with largest being 310 lines. All functionality preserved. Ready for AI-friendly editing."
```

This ensures proper task tracking and communication of completion status.