import customtkinter as ctk
from ..config.ui_constants import UI_TEXT, UI_DIMENSIONS


class LogsTab:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.setup_tab()
    
    def setup_tab(self):
        """Setup logging tab"""
        logs_tab = self.parent.add(UI_TEXT['logs_tab'])
        
        # Log display
        self.log_display = ctk.CTkTextbox(logs_tab, height=UI_DIMENSIONS['log_display_height'])
        self.log_display.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Log controls
        log_controls = ctk.CTkFrame(logs_tab)
        log_controls.pack(fill="x", padx=20, pady=(0, 20))
        
        self.clear_logs_button = ctk.CTkButton(
            log_controls,
            text=UI_TEXT['clear_logs_btn'],
            command=self.clear_logs
        )
        self.clear_logs_button.pack(side="left", padx=10)
    
    def clear_logs(self):
        """Clear the log display"""
        self.log_display.delete("0.0", "end")
    
    def add_log_message(self, message):
        """Add message to log display"""
        self.log_display.insert("end", f"{message}\n")
        self.log_display.see("end")