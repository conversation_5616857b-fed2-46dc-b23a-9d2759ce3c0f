import threading
from datetime import datetime


class BackgroundWorker:
    """Helper class for managing background tasks"""
    
    @staticmethod
    def run_in_background(target, args=(), daemon=True):
        """Run a function in a background thread"""
        thread = threading.Thread(target=target, args=args, daemon=daemon)
        thread.start()
        return thread
    
    @staticmethod
    def schedule_ui_update(root, callback, delay=0):
        """Schedule a UI update on the main thread"""
        root.after(delay, callback)


class Logger:
    """Simple logging helper"""
    
    @staticmethod
    def format_message(message):
        """Format message with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        return f"[{timestamp}] {message}"