import customtkinter as ctk
import threading
import webbrowser
from datetime import datetime
from tkinter import messagebox, simpledialog
import tkinter as tk

from .config.app_settings import APP_NAME, STEAM_PATH, DATABASE_FILE, KEY_FILE, LOCK_FILE
from .config.ui_constants import UI_TEXT, UI_DIMENSIONS, UI_COLORS, THEME_OPTIONS, DEFAULT_THEME
from .components.theme_manager import ThemeManager
from .components.system_tray import SystemTray
from .views.dashboard_view import DashboardView
from .views.admin_view import AdminView

from gui_backend.encryption import initialize_encryption
from gui_backend.database import checkDatabase, read_all_license, read_license, read_all_app_id
from gui_backend.auth_service import initialize_keyauth, authenticate_license
from gui_backend.manifest_api import process_manifest, update_manifest, get_manifest
from gui_backend.steam_operations import remove_lock, clearCache
from gui_backend.system_tools import addDefenderExclusion, checkKoa<PERSON>oader, checkG<PERSON><PERSON>uma, checkMiniTool


class ModernSteamManifestGUI:
    def __init__(self):
        # Initialize CustomTkinter with modern theme
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        self.root = ctk.CTk()
        self.root.title(UI_TEXT['window_title'])
        self.root.geometry(f"{UI_DIMENSIONS['window_width']}x{UI_DIMENSIONS['window_height']}")
        self.root.minsize(UI_DIMENSIONS['min_width'], UI_DIMENSIONS['min_height'])
        
        # Application state
        self.authenticated = False
        self.current_license = None
        self.keyauthapp = None
        self.server_url = None
        self.encryption_key = None
        self.admin_mode = False
        self.current_view = "dashboard"
        self.steam_path = STEAM_PATH
        
        # Initialize backend services
        self._initialize_backend()
        
        # Setup modern UI
        self.setup_modern_ui()
        
        # Bind keyboard shortcuts
        self.setup_keyboard_shortcuts()
        
        # Initialize app
        self.initialize_app()
        
        # Setup system tray
        self.system_tray = SystemTray(self)
        try:
            self.system_tray.setup_system_tray()
        except:
            pass
    
    def _initialize_backend(self):
        """Initialize backend services"""
        try:
            # Initialize encryption
            self.encryption_key = initialize_encryption(KEY_FILE)
            
            # Initialize KeyAuth
            self.keyauthapp = initialize_keyauth()
            self.server_url = self.keyauthapp.var("SERVER_API") or "https://manifest.online-mtyb.com"
            
        except Exception as e:
            self.log_message(f"[MTYB] Backend initialization error: {str(e)}")
    
    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for admin access"""
        self.root.bind('<Control-Shift-A>', self.toggle_admin_access)
        self.root.bind('<Control-Shift-D>', lambda e: self.switch_view("dashboard"))
        self.root.bind('<Escape>', self.close_admin_panel)
    
    def setup_modern_ui(self):
        """Setup the modern UI with dashboard design"""
        # Main container
        self.main_container = ctk.CTkFrame(self.root, fg_color="transparent")
        self.main_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Header
        self.setup_header()
        
        # Navigation sidebar
        self.setup_navigation()
        
        # Content area
        self.setup_content_area()
        
        # Status bar
        self.setup_status_bar()
        
        # Initially show dashboard
        self.show_dashboard()
    
    def setup_header(self):
        """Setup modern header with title and quick actions"""
        header_frame = ctk.CTkFrame(self.main_container, height=80, corner_radius=UI_DIMENSIONS['card_corner_radius'])
        header_frame.pack(fill="x", pady=(0, UI_DIMENSIONS['section_spacing']))
        header_frame.pack_propagate(False)
        
        # Title section
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="left", fill="y", padx=UI_DIMENSIONS['content_padding'])
        
        title_label = ctk.CTkLabel(
            title_frame,
            text=UI_TEXT['window_title'],
            font=ctk.CTkFont(size=UI_DIMENSIONS['title_font_size'], weight="bold")
        )
        title_label.pack(anchor="w", pady=(10, 0))
        
        subtitle_label = ctk.CTkLabel(
            title_frame,
            text=UI_TEXT['app_subtitle'],
            font=ctk.CTkFont(size=UI_DIMENSIONS['subtitle_font_size']),
            text_color=UI_COLORS['text_secondary']
        )
        subtitle_label.pack(anchor="w")
        
        # Quick actions
        actions_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        actions_frame.pack(side="right", fill="y", padx=UI_DIMENSIONS['content_padding'])
        
        self.auth_status_indicator = ctk.CTkLabel(
            actions_frame,
            text="●",
            font=ctk.CTkFont(size=16),
            text_color=UI_COLORS['error']
        )
        self.auth_status_indicator.pack(side="right", padx=(10, 0), pady=25)
        
        self.auth_status_label = ctk.CTkLabel(
            actions_frame,
            text=UI_TEXT['status_not_auth'],
            font=ctk.CTkFont(size=UI_DIMENSIONS['body_font_size'])
        )
        self.auth_status_label.pack(side="right", pady=25)
    
    def setup_navigation(self):
        """Setup navigation sidebar"""
        nav_frame = ctk.CTkFrame(
            self.main_container,
            width=UI_DIMENSIONS['sidebar_width'],
            corner_radius=UI_DIMENSIONS['card_corner_radius']
        )
        nav_frame.pack(side="left", fill="y", padx=(0, UI_DIMENSIONS['section_spacing']))
        nav_frame.pack_propagate(False)
        
        # Navigation title
        nav_title = ctk.CTkLabel(
            nav_frame,
            text="Navigation",
            font=ctk.CTkFont(size=UI_DIMENSIONS['heading_font_size'], weight="bold")
        )
        nav_title.pack(pady=(20, 10))
        
        # Navigation buttons
        self.nav_buttons = {}
        nav_items = [
            ("dashboard", UI_TEXT['dashboard_title']),
            ("auth", UI_TEXT['auth_title']),
            ("manifest", UI_TEXT['manifest_title']),
            ("license", UI_TEXT['license_title']),
            ("logs", UI_TEXT['logs_title']),
            ("settings", UI_TEXT['settings_title'])
        ]
        
        for view_id, title in nav_items:
            btn = ctk.CTkButton(
                nav_frame,
                text=title,
                width=UI_DIMENSIONS['nav_button_width'],
                height=UI_DIMENSIONS['nav_button_height'],
                command=lambda v=view_id: self.switch_view(v),
                anchor="w"
            )
            btn.pack(pady=5, padx=10)
            self.nav_buttons[view_id] = btn
        
        # Admin access hint (subtle)
        if not self.admin_mode:
            admin_hint = ctk.CTkLabel(
                nav_frame,
                text=UI_TEXT['admin_access_hint'],
                font=ctk.CTkFont(size=UI_DIMENSIONS['small_font_size']),
                text_color=UI_COLORS['text_muted']
            )
            admin_hint.pack(side="bottom", pady=10)
    
    def setup_content_area(self):
        """Setup main content area"""
        self.content_frame = ctk.CTkFrame(
            self.main_container,
            corner_radius=UI_DIMENSIONS['card_corner_radius']
        )
        self.content_frame.pack(side="right", fill="both", expand=True)
        
        # Content will be dynamically loaded based on current view
    
    def setup_status_bar(self):
        """Setup status bar at bottom"""
        self.status_frame = ctk.CTkFrame(
            self.main_container,
            height=40,
            corner_radius=UI_DIMENSIONS['card_corner_radius']
        )
        self.status_frame.pack(fill="x", pady=(UI_DIMENSIONS['section_spacing'], 0))
        self.status_frame.pack_propagate(False)
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text=UI_TEXT['status_ready'],
            font=ctk.CTkFont(size=UI_DIMENSIONS['body_font_size'])
        )
        self.status_label.pack(side="left", padx=20, pady=10)
        
        # Theme toggle
        theme_btn = ctk.CTkButton(
            self.status_frame,
            text="🌓",
            width=30,
            height=30,
            command=self.toggle_theme
        )
        theme_btn.pack(side="right", padx=10, pady=5)
    
    def switch_view(self, view_id):
        """Switch between different views"""
        self.current_view = view_id
        
        # Update navigation button states
        for btn_id, btn in self.nav_buttons.items():
            if btn_id == view_id:
                btn.configure(fg_color=UI_COLORS['primary'])
            else:
                btn.configure(fg_color=["#3B8ED0", "#1F6AA5"])  # Default colors
        
        # Clear content frame
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # Load appropriate view
        if view_id == "dashboard":
            self.show_dashboard()
        elif view_id == "auth":
            self.show_auth_view()
        elif view_id == "manifest":
            self.show_manifest_view()
        elif view_id == "license":
            self.show_license_view()
        elif view_id == "logs":
            self.show_logs_view()
        elif view_id == "settings":
            self.show_settings_view()
        elif view_id == "admin" and self.admin_mode:
            self.show_admin_view()
    
    def show_dashboard(self):
        """Show modern dashboard view"""
        DashboardView(self.content_frame, self)
    
    def toggle_admin_access(self, event=None):
        """Toggle admin access with password prompt"""
        if not self.admin_mode:
            password = simpledialog.askstring(
                UI_TEXT['admin_unlock_title'],
                UI_TEXT['admin_unlock_message'],
                show='*'
            )
            
            # Simple password check (in real app, use proper authentication)
            if password == "admin123":  # Change this to a secure password
                self.admin_mode = True
                self.update_navigation_for_admin()
                messagebox.showinfo("Success", UI_TEXT['admin_access_granted'])
            else:
                messagebox.showerror("Error", UI_TEXT['admin_access_denied'])
        else:
            self.admin_mode = False
            self.update_navigation_for_admin()
    
    def update_navigation_for_admin(self):
        """Update navigation to show/hide admin options"""
        if self.admin_mode:
            # Add admin button to navigation
            if "admin" not in self.nav_buttons:
                admin_btn = ctk.CTkButton(
                    self.nav_buttons["settings"].master,
                    text="🔧 Admin Tools",
                    width=UI_DIMENSIONS['nav_button_width'],
                    height=UI_DIMENSIONS['nav_button_height'],
                    command=lambda: self.switch_view("admin"),
                    anchor="w",
                    fg_color=UI_COLORS['error']
                )
                admin_btn.pack(pady=5, padx=10)
                self.nav_buttons["admin"] = admin_btn
        else:
            # Remove admin button
            if "admin" in self.nav_buttons:
                self.nav_buttons["admin"].destroy()
                del self.nav_buttons["admin"]
    
    def show_auth_view(self):
        """Show authentication view"""
        auth_label = ctk.CTkLabel(
            self.content_frame,
            text="🔐 Authentication View - Coming Soon",
            font=ctk.CTkFont(size=20)
        )
        auth_label.pack(expand=True)

    def show_manifest_view(self):
        """Show manifest management view"""
        manifest_label = ctk.CTkLabel(
            self.content_frame,
            text="📦 Manifest Management - Coming Soon",
            font=ctk.CTkFont(size=20)
        )
        manifest_label.pack(expand=True)

    def show_license_view(self):
        """Show license management view"""
        license_label = ctk.CTkLabel(
            self.content_frame,
            text="📋 License Management - Coming Soon",
            font=ctk.CTkFont(size=20)
        )
        license_label.pack(expand=True)

    def show_logs_view(self):
        """Show logs view"""
        logs_label = ctk.CTkLabel(
            self.content_frame,
            text="📝 Logs View - Coming Soon",
            font=ctk.CTkFont(size=20)
        )
        logs_label.pack(expand=True)

    def show_settings_view(self):
        """Show settings view"""
        settings_label = ctk.CTkLabel(
            self.content_frame,
            text="⚙️ Settings View - Coming Soon",
            font=ctk.CTkFont(size=20)
        )
        settings_label.pack(expand=True)

    def show_admin_view(self):
        """Show admin panel"""
        AdminView(self.content_frame, self)

    def close_admin_panel(self, event=None):
        """Close admin panel and return to dashboard"""
        if self.current_view == "admin":
            self.switch_view("dashboard")

    def toggle_theme(self):
        """Toggle between light and dark themes"""
        current_mode = ctk.get_appearance_mode()
        new_mode = "dark" if current_mode == "light" else "light"
        ctk.set_appearance_mode(new_mode)
        self.log_message(f"Theme changed to {new_mode}")

    def initialize_app(self):
        """Initialize application"""
        try:
            checkDatabase(DATABASE_FILE)
            self.log_message("Application initialized successfully")
        except Exception as e:
            self.log_message(f"Initialization error: {str(e)}")

    def log_message(self, message):
        """Log message to console and status"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
        self.status_label.configure(text=message)

    def _update_all_background(self):
        """Background task for updating all manifests"""
        try:
            self.log_message("Starting manifest update...")
            # Implement update logic here
            self.log_message("All manifests updated successfully")
        except Exception as e:
            self.log_message(f"Update failed: {str(e)}")


def main_gui():
    """Main entry point for the modern GUI"""
    app = ModernSteamManifestGUI()
    app.root.mainloop()


if __name__ == '__main__':
    main_gui()
