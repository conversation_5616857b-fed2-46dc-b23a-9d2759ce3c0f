import customtkinter as ctk
from datetime import datetime
import threading
from ..config.ui_constants import UI_TEXT, UI_DIMENSIONS, UI_COLORS


class DashboardView:
    def __init__(self, parent_frame, main_app):
        self.parent_frame = parent_frame
        self.main_app = main_app
        self.setup_dashboard()
    
    def setup_dashboard(self):
        """Setup modern dashboard with cards and quick actions"""
        # Main dashboard container
        dashboard_container = ctk.CTkScrollableFrame(
            self.parent_frame,
            fg_color="transparent"
        )
        dashboard_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Welcome section
        self.setup_welcome_section(dashboard_container)
        
        # Quick actions grid
        self.setup_quick_actions(dashboard_container)
        
        # Status cards
        self.setup_status_cards(dashboard_container)
        
        # Recent activity
        self.setup_recent_activity(dashboard_container)
    
    def setup_welcome_section(self, parent):
        """Setup welcome section with user status"""
        welcome_frame = ctk.CTkFrame(
            parent,
            height=120,
            corner_radius=UI_DIMENSIONS['card_corner_radius']
        )
        welcome_frame.pack(fill="x", pady=(0, 20))
        welcome_frame.pack_propagate(False)
        
        # Welcome text
        welcome_text = "Welcome back!" if self.main_app.authenticated else "Welcome to MTYB Steam Tool"
        welcome_label = ctk.CTkLabel(
            welcome_frame,
            text=welcome_text,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        welcome_label.pack(pady=(20, 5))
        
        # Status text
        if self.main_app.authenticated:
            status_text = f"✅ Authenticated • Ready to manage manifests"
            status_color = UI_COLORS['success']
        else:
            status_text = "🔐 Please authenticate to access all features"
            status_color = UI_COLORS['warning']
        
        status_label = ctk.CTkLabel(
            welcome_frame,
            text=status_text,
            font=ctk.CTkFont(size=14),
            text_color=status_color
        )
        status_label.pack()
        
        # Quick auth button if not authenticated
        if not self.main_app.authenticated:
            auth_btn = ctk.CTkButton(
                welcome_frame,
                text="🔐 Authenticate Now",
                width=200,
                height=35,
                command=lambda: self.main_app.switch_view("auth")
            )
            auth_btn.pack(pady=(10, 0))
    
    def setup_quick_actions(self, parent):
        """Setup quick action buttons in a grid"""
        actions_frame = ctk.CTkFrame(
            parent,
            corner_radius=UI_DIMENSIONS['card_corner_radius']
        )
        actions_frame.pack(fill="x", pady=(0, 20))
        
        # Title
        title_label = ctk.CTkLabel(
            actions_frame,
            text=UI_TEXT['quick_actions'],
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 15))
        
        # Actions grid
        grid_frame = ctk.CTkFrame(actions_frame, fg_color="transparent")
        grid_frame.pack(padx=20, pady=(0, 20))
        
        # Quick action buttons
        actions = [
            ("📦 Download Manifest", self.quick_download_manifest, UI_COLORS['primary']),
            ("🔄 Update All", self.quick_update_all, UI_COLORS['info']),
            ("🚀 Launch Steam", self.quick_launch_steam, UI_COLORS['success']),
            ("📋 View Licenses", lambda: self.main_app.switch_view("license"), UI_COLORS['secondary'])
        ]
        
        for i, (text, command, color) in enumerate(actions):
            row = i // 2
            col = i % 2
            
            btn = ctk.CTkButton(
                grid_frame,
                text=text,
                width=UI_DIMENSIONS['dashboard_card_width'],
                height=60,
                command=command,
                fg_color=color,
                hover_color=self.darken_color(color),
                font=ctk.CTkFont(size=14, weight="bold")
            )
            btn.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # Configure grid weights
        grid_frame.grid_columnconfigure(0, weight=1)
        grid_frame.grid_columnconfigure(1, weight=1)
    
    def setup_status_cards(self, parent):
        """Setup status information cards"""
        status_container = ctk.CTkFrame(
            parent,
            corner_radius=UI_DIMENSIONS['card_corner_radius']
        )
        status_container.pack(fill="x", pady=(0, 20))
        
        # Title
        title_label = ctk.CTkLabel(
            status_container,
            text=UI_TEXT['system_status'],
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 15))
        
        # Status cards grid
        cards_frame = ctk.CTkFrame(status_container, fg_color="transparent")
        cards_frame.pack(padx=20, pady=(0, 20))
        
        # Status information
        steam_status = "✅ Detected" if self.main_app.steam_path else "❌ Not Found"
        auth_status = "✅ Active" if self.main_app.authenticated else "❌ Required"
        
        status_items = [
            ("Steam Installation", steam_status),
            ("Authentication", auth_status),
            ("Server Connection", "✅ Online"),
            ("Last Update", datetime.now().strftime("%H:%M"))
        ]
        
        for i, (label, value) in enumerate(status_items):
            row = i // 2
            col = i % 2
            
            card = ctk.CTkFrame(
                cards_frame,
                width=UI_DIMENSIONS['dashboard_card_width'],
                height=80
            )
            card.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
            card.pack_propagate(False)
            
            # Label
            label_widget = ctk.CTkLabel(
                card,
                text=label,
                font=ctk.CTkFont(size=12, weight="bold")
            )
            label_widget.pack(pady=(15, 5))
            
            # Value
            value_widget = ctk.CTkLabel(
                card,
                text=value,
                font=ctk.CTkFont(size=14)
            )
            value_widget.pack()
        
        # Configure grid weights
        cards_frame.grid_columnconfigure(0, weight=1)
        cards_frame.grid_columnconfigure(1, weight=1)
    
    def setup_recent_activity(self, parent):
        """Setup recent activity log"""
        activity_frame = ctk.CTkFrame(
            parent,
            corner_radius=UI_DIMENSIONS['card_corner_radius']
        )
        activity_frame.pack(fill="x", pady=(0, 20))
        
        # Title
        title_label = ctk.CTkLabel(
            activity_frame,
            text=UI_TEXT['recent_activity'],
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 15))
        
        # Activity list
        self.activity_list = ctk.CTkTextbox(
            activity_frame,
            height=150,
            font=ctk.CTkFont(size=12)
        )
        self.activity_list.pack(fill="x", padx=20, pady=(0, 20))
        
        # Add some sample activity
        self.add_activity("Application started")
        if self.main_app.authenticated:
            self.add_activity("User authenticated successfully")
    
    def add_activity(self, message):
        """Add activity to the recent activity log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        activity_text = f"[{timestamp}] {message}\n"
        self.activity_list.insert("end", activity_text)
        self.activity_list.see("end")
    
    def quick_download_manifest(self):
        """Quick action to download manifest"""
        if not self.main_app.authenticated:
            self.main_app.switch_view("auth")
        else:
            self.main_app.switch_view("manifest")
    
    def quick_update_all(self):
        """Quick action to update all manifests"""
        if not self.main_app.authenticated:
            self.main_app.switch_view("auth")
        else:
            # Implement update all logic
            self.add_activity("Updating all manifests...")
            threading.Thread(target=self.main_app._update_all_background, daemon=True).start()
    
    def quick_launch_steam(self):
        """Quick action to launch Steam"""
        try:
            import subprocess
            import os
            
            if os.name == 'nt':  # Windows
                subprocess.Popen(['steam://'])
            self.add_activity("Steam launched")
        except Exception as e:
            self.add_activity(f"Failed to launch Steam: {str(e)}")
    
    def darken_color(self, color):
        """Darken a hex color for hover effects"""
        # Simple color darkening - in a real app, use proper color manipulation
        color_map = {
            UI_COLORS['primary']: UI_COLORS['primary_hover'],
            UI_COLORS['info']: '#2563eb',
            UI_COLORS['success']: '#059669',
            UI_COLORS['secondary']: UI_COLORS['secondary_hover']
        }
        return color_map.get(color, color)
