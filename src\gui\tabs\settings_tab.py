import customtkinter as ctk
from ..config.ui_constants import UI_TEXT, UI_DIMENSIONS, THEME_OPTIONS, DEFAULT_THEME
from ..components.theme_manager import ThemeManager


class SettingsTab:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.setup_tab()
    
    def setup_tab(self):
        """Setup settings tab"""
        settings_tab = self.parent.add(UI_TEXT['settings_tab'])
        
        settings_frame = ctk.CTkFrame(settings_tab)
        settings_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Theme settings
        ctk.CTkLabel(settings_frame, text=UI_TEXT['appearance'], 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(20, 10))
        
        theme_frame = ctk.CTkFrame(settings_frame)
        theme_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(theme_frame, text=UI_TEXT['theme_label']).pack(side="left", padx=20)
        
        self.theme_var = ctk.StringVar(value=DEFAULT_THEME)
        self.theme_menu = ctk.CTkOptionMenu(
            theme_frame,
            values=THEME_OPTIONS,
            variable=self.theme_var,
            command=self.change_theme
        )
        self.theme_menu.pack(side="left", padx=10)
        
        # Steam path display
        ctk.CTkLabel(settings_frame, text=UI_TEXT['steam_config'], 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(20, 10))
        
        steam_frame = ctk.CTkFrame(settings_frame)
        steam_frame.pack(fill="x", padx=20, pady=10)
        
        try:
            from gui_backend.steam_operations import getSteamPath
            steam_path = getSteamPath()
            path_text = UI_TEXT['steam_path_label'].format(steam_path)
        except:
            path_text = UI_TEXT['steam_path_not_detected']
        
        ctk.CTkLabel(steam_frame, text=path_text).pack(padx=20, pady=10)
    
    def change_theme(self, choice):
        """Change theme based on dropdown selection"""
        theme_map = {
            "System": "system",
            "Light": "light", 
            "Dark": "dark"
        }
        
        theme = theme_map.get(choice, "system")
        ThemeManager.apply_theme(theme)
        self.main_app.log_message(f"[MTYB] Theme changed to: {choice}")