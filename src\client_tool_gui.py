﻿# Modern GUI entry point for MTYB Steam Manifest Tool
# Features modern design with dashboard, hidden admin panel, and improved UX

import customtkinter as ctk
from gui.modern_main_window import main_gui

# Configure customtkinter for modern appearance
ctk.set_appearance_mode("system")  # Auto-detect system theme
ctk.set_default_color_theme("blue")  # Modern blue theme

if __name__ == '__main__':
    print("🎮 Starting MTYB Steam Manifest Tool")
    print("💡 Features:")
    print("   • Modern dashboard design")
    print("   • Hidden admin panel (Ctrl+Shift+A)")
    print("   • Improved user experience")
    print("   • Card-based layout")
    print("   • Better visual feedback")
    print()
    
    main_gui()
