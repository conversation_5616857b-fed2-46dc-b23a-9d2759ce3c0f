# UI Text Constants
UI_TEXT = {
    'window_title': 'MTYB Steam Manifest Tool',
    'auth_tab': 'Authentication', 
    'manifest_tab': 'Manifest Management',
    'license_tab': 'License Management',
    'logs_tab': 'Logs',
    'settings_tab': 'Settings',
    'license_key_label': 'License Key:',
    'license_placeholder': 'Enter your license key',
    'authenticate_btn': 'Authenticate',
    'authenticating_btn': 'Authenticating...',
    'authenticated_btn': 'Authenticated ✓',
    'app_id_label': 'App ID:',
    'app_id_placeholder': 'Enter Steam App ID',
    'download_manifest_btn': 'Download Manifest',
    'update_all_btn': 'Update All',
    'launch_steam_btn': 'Launch Steam',
    'refresh_list_btn': 'Refresh List',
    'clear_logs_btn': 'Clear Logs',
    'toggle_theme_btn': 'Toggle Theme',
    'status_not_auth': 'Status: Not authenticated',
    'status_auth': 'Status: Authenticated',
    'auth_welcome': 'Enter your license key and click Authenticate to begin.',
    'ready': 'Ready',
    'downloading': 'Downloading manifest...',
    'download_complete': 'Download complete!',
    'download_failed': 'Download failed',
    'registered_licenses': 'Registered Licenses',
    'appearance': 'Appearance',
    'steam_config': 'Steam Configuration',
    'theme_label': 'Theme:',
    'steam_path_label': 'Steam Path: {}',
    'steam_path_not_detected': 'Steam Path: Not detected'
}

# UI Dimensions
UI_DIMENSIONS = {
    'window_width': 800,
    'window_height': 600,
    'min_width': 600,
    'min_height': 400,
    'license_entry_width': 400,
    'license_entry_height': 40,
    'auth_button_width': 200,
    'auth_button_height': 40,
    'app_id_entry_width': 200,
    'auth_status_height': 200,
    'auth_status_width': 500,
    'manifest_info_height': 250,
    'license_display_height': 300,
    'log_display_height': 400,
    'progress_bar_width': 400,
    'theme_button_width': 100,
    'tabview_width': 700,
    'tabview_height': 400
}

# Theme Options
THEME_OPTIONS = ["System", "Light", "Dark"]
DEFAULT_THEME = "System"