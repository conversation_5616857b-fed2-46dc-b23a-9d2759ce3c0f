import customtkinter as ctk
from tkinter import messagebox
import threading
from ..config.ui_constants import UI_TEXT, UI_DIMENSIONS


class ManifestTab:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.setup_tab()
    
    def setup_tab(self):
        """Setup manifest management tab"""
        manifest_tab = self.parent.add(UI_TEXT['manifest_tab'])
        
        # Controls frame
        controls_frame = ctk.CTkFrame(manifest_tab)
        controls_frame.pack(fill="x", padx=20, pady=20)
        
        # App ID input
        ctk.CTkLabel(controls_frame, text=UI_TEXT['app_id_label'], 
                    font=ctk.CTkFont(size=14, weight="bold")).pack(side="left", padx=(20, 10))
        
        self.app_id_entry = ctk.CTkEntry(
            controls_frame,
            placeholder_text=UI_TEXT['app_id_placeholder'],
            width=UI_DIMENSIONS['app_id_entry_width']
        )
        self.app_id_entry.pack(side="left", padx=10)
        
        # Download button
        self.download_button = ctk.CTkButton(
            controls_frame,
            text=UI_TEXT['download_manifest_btn'],
            command=self.download_manifest,
            state="disabled"
        )
        self.download_button.pack(side="left", padx=10)
        
        # Update all button
        self.update_all_button = ctk.CTkButton(
            controls_frame,
            text=UI_TEXT['update_all_btn'],
            command=self.update_all_manifests,
            state="disabled"
        )
        self.update_all_button.pack(side="left", padx=10)
        
        # Launch Steam button
        self.launch_button = ctk.CTkButton(
            controls_frame,
            text=UI_TEXT['launch_steam_btn'],
            command=self.launch_steam,
            state="disabled"
        )
        self.launch_button.pack(side="right", padx=20)
        
        # Progress frame
        progress_frame = ctk.CTkFrame(manifest_tab)
        progress_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.progress_label = ctk.CTkLabel(progress_frame, text=UI_TEXT['ready'])
        self.progress_label.pack(pady=10)
        
        self.progress_bar = ctk.CTkProgressBar(progress_frame, width=UI_DIMENSIONS['progress_bar_width'])
        self.progress_bar.pack(pady=10)
        self.progress_bar.set(0)
        
        # Manifest info display
        self.manifest_info = ctk.CTkTextbox(manifest_tab, height=UI_DIMENSIONS['manifest_info_height'])
        self.manifest_info.pack(fill="both", expand=True, padx=20, pady=(0, 20))
    
    def download_manifest(self):
        """Download manifest for specified app ID"""
        app_id = self.app_id_entry.get().strip()
        if not app_id:
            messagebox.showerror("Error", "Please enter an App ID")
            return
        
        if not self.main_app.authenticated:
            messagebox.showerror("Error", "Please authenticate first")
            return
        
        # Run download in background
        threading.Thread(
            target=self.main_app._download_manifest_background,
            args=(app_id,),
            daemon=True
        ).start()
    
    def update_all_manifests(self):
        """Update all registered app manifests"""
        if not self.main_app.authenticated:
            messagebox.showerror("Error", "Please authenticate first")
            return
        
        threading.Thread(target=self.main_app._update_all_background, daemon=True).start()
    
    def launch_steam(self):
        """Launch Steam with current app"""
        self.main_app.launch_steam()
    
    def enable_controls(self):
        """Enable manifest management controls"""
        self.download_button.configure(state="normal")
        self.update_all_button.configure(state="normal")
        self.launch_button.configure(state="normal")
    
    def update_progress(self, value, text=None):
        """Update progress bar and label"""
        self.progress_bar.set(value)
        if text:
            self.progress_label.configure(text=text)
    
    def set_manifest_info(self, text):
        """Set manifest info text"""
        self.manifest_info.delete("0.0", "end")
        self.manifest_info.insert("0.0", text)