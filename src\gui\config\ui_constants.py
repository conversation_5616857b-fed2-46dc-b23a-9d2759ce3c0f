# UI Text Constants
UI_TEXT = {
    # Main Window
    'window_title': 'MTYB Steam Manifest Tool',
    'app_subtitle': 'Manage your Steam game manifests with ease',

    # Dashboard
    'dashboard_title': '🎮 Dashboard',
    'quick_actions': 'Quick Actions',
    'recent_activity': 'Recent Activity',
    'system_status': 'System Status',

    # Authentication
    'auth_title': '🔐 Authentication',
    'license_key_label': 'License Key',
    'license_placeholder': 'Enter your license key here...',
    'authenticate_btn': 'Authenticate',
    'authenticating_btn': 'Authenticating...',
    'authenticated_btn': '✓ Authenticated',
    'auth_welcome': 'Welcome! Please enter your license key to get started.',
    'auth_success': 'Authentication successful! You can now access all features.',
    'auth_failed': 'Authentication failed. Please check your license key.',

    # Manifest Management
    'manifest_title': '📦 Manifest Manager',
    'app_id_label': 'Steam App ID',
    'app_id_placeholder': 'Enter Steam App ID (e.g., 730)',
    'download_manifest_btn': 'Download Manifest',
    'update_all_btn': 'Update All Manifests',
    'manifest_info': 'Manifest Information',
    'download_progress': 'Download Progress',

    # License Management
    'license_title': '📋 License Manager',
    'registered_licenses': 'Registered Licenses',
    'license_details': 'License Details',
    'refresh_list_btn': 'Refresh',
    'no_licenses': 'No licenses found. Please authenticate first.',

    # System Tools (Admin)
    'admin_title': '⚙️ System Tools',
    'admin_warning': '⚠️ Administrator Features - Use with caution',
    'defender_exclusion_btn': 'Add Defender Exclusion',
    'check_koaloader_btn': 'Check KoaLoader',
    'check_greenluma_btn': 'Check GreenLuma',
    'check_minitool_btn': 'Check MiniTool',
    'system_tools_desc': 'Advanced system integration tools',

    # Logs
    'logs_title': '📝 Activity Logs',
    'clear_logs_btn': 'Clear Logs',
    'export_logs_btn': 'Export Logs',
    'log_level_label': 'Log Level:',

    # Settings
    'settings_title': '⚙️ Settings',
    'appearance_section': 'Appearance',
    'steam_section': 'Steam Configuration',
    'advanced_section': 'Advanced Settings',
    'theme_label': 'Theme',
    'steam_path_label': 'Steam Installation: {}',
    'steam_path_not_detected': 'Steam Installation: Not detected',
    'auto_update_label': 'Auto-update manifests',
    'notifications_label': 'Show notifications',

    # Status Messages
    'status_not_auth': 'Not authenticated',
    'status_auth': 'Authenticated',
    'status_ready': 'Ready',
    'status_downloading': 'Downloading...',
    'status_updating': 'Updating...',
    'status_complete': 'Complete',
    'status_error': 'Error',

    # Actions
    'launch_steam_btn': 'Launch Steam',
    'open_steam_folder_btn': 'Open Steam Folder',
    'toggle_theme_btn': 'Toggle Theme',
    'minimize_to_tray_btn': 'Minimize to Tray',

    # Admin Access
    'admin_access_hint': 'Press Ctrl+Shift+A for advanced features',
    'admin_unlock_title': 'Administrator Access',
    'admin_unlock_message': 'Enter administrator password:',
    'admin_password_placeholder': 'Password...',
    'admin_unlock_btn': 'Unlock',
    'admin_access_granted': 'Administrator access granted',
    'admin_access_denied': 'Access denied',

    # Classic UI Tab Names (for backward compatibility)
    'auth_tab': 'Authentication',
    'manifest_tab': 'Manifest Manager',
    'license_tab': 'License Manager',
    'logs_tab': 'Logs',
    'settings_tab': 'Settings',

    # Classic UI Authentication
    'license_key_label': 'License Key:',
    'license_placeholder': 'Enter your license key...',
    'authenticate_btn': 'Authenticate',
    'authenticating_btn': 'Authenticating...',
    'authenticated_btn': 'Authenticated ✓',
    'auth_welcome': 'Welcome to MTYB Steam Manifest Tool\n\nPlease enter your license key to authenticate.',

    # Classic UI Manifest Management
    'app_id_label': 'Steam App ID:',
    'app_id_placeholder': 'Enter Steam App ID...',
    'download_manifest_btn': 'Download Manifest',
    'update_all_btn': 'Update All Manifests',
    'launch_steam_btn': 'Launch Steam',
    'ready': 'Ready',

    # Classic UI License Management
    'registered_licenses': 'Registered Licenses:',
    'refresh_list_btn': 'Refresh List',

    # Classic UI Logs
    'clear_logs_btn': 'Clear Logs',

    # Classic UI Settings
    'appearance': 'Appearance',
    'theme_label': 'Theme:',
    'steam_config': 'Steam Configuration',
    'steam_path_label': 'Steam Path: {}',
    'steam_path_not_detected': 'Steam path not detected'
}

# UI Dimensions - Modern Design
UI_DIMENSIONS = {
    # Main Window
    'window_width': 1000,
    'window_height': 700,
    'min_width': 800,
    'min_height': 600,

    # Classic UI dimensions (for backward compatibility)
    'tabview_width': 760,
    'tabview_height': 500,
    'theme_button_width': 100,

    # Cards and Containers
    'card_padding': 20,
    'card_corner_radius': 12,
    'section_spacing': 15,
    'content_padding': 25,

    # Dashboard
    'dashboard_card_width': 300,
    'dashboard_card_height': 150,
    'quick_action_btn_width': 140,
    'quick_action_btn_height': 45,
    'status_indicator_size': 12,

    # Authentication
    'license_entry_width': 400,
    'license_entry_height': 45,
    'auth_button_width': 200,
    'auth_button_height': 45,
    'auth_card_width': 500,
    'auth_card_height': 300,
    'auth_status_height': 200,
    'auth_status_width': 500,

    # Manifest Management
    'app_id_entry_width': 250,
    'app_id_entry_height': 40,
    'manifest_card_height': 400,
    'progress_bar_width': 350,
    'progress_bar_height': 8,
    'manifest_info_height': 250,

    # License Display
    'license_card_height': 350,
    'license_item_height': 60,
    'license_display_height': 300,

    # Logs
    'log_display_height': 400,
    'log_card_height': 450,

    # Settings
    'settings_card_height': 500,
    'setting_item_height': 50,

    # Admin Panel
    'admin_panel_width': 600,
    'admin_panel_height': 500,
    'admin_button_width': 180,
    'admin_button_height': 40,

    # Navigation
    'nav_button_width': 120,
    'nav_button_height': 40,
    'sidebar_width': 200,

    # Typography
    'title_font_size': 24,
    'subtitle_font_size': 14,
    'heading_font_size': 18,
    'body_font_size': 12,
    'small_font_size': 10
}

# Modern Color Scheme
UI_COLORS = {
    # Primary Colors
    'primary': '#2563eb',
    'primary_hover': '#1d4ed8',
    'primary_light': '#dbeafe',

    # Secondary Colors
    'secondary': '#64748b',
    'secondary_hover': '#475569',
    'secondary_light': '#f1f5f9',

    # Status Colors
    'success': '#10b981',
    'success_light': '#d1fae5',
    'warning': '#f59e0b',
    'warning_light': '#fef3c7',
    'error': '#ef4444',
    'error_light': '#fee2e2',
    'info': '#3b82f6',
    'info_light': '#dbeafe',

    # Background Colors
    'bg_primary': '#ffffff',
    'bg_secondary': '#f8fafc',
    'bg_card': '#ffffff',
    'bg_hover': '#f1f5f9',

    # Text Colors
    'text_primary': '#1e293b',
    'text_secondary': '#64748b',
    'text_muted': '#94a3b8',
    'text_inverse': '#ffffff',

    # Border Colors
    'border_light': '#e2e8f0',
    'border_medium': '#cbd5e1',
    'border_dark': '#94a3b8'
}

# Theme Options
THEME_OPTIONS = ["System", "Light", "Dark", "Blue", "Green"]
DEFAULT_THEME = "System"

# Animation Settings
ANIMATION_SETTINGS = {
    'fade_duration': 200,
    'slide_duration': 300,
    'bounce_duration': 400,
    'hover_duration': 150
}