import customtkinter as ctk
from ..config.ui_constants import UI_TEXT, UI_DIMENSIONS


class LicenseTab:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.setup_tab()
    
    def setup_tab(self):
        """Setup license management tab"""
        licenses_tab = self.parent.add(UI_TEXT['license_tab'])
        
        # License list frame
        list_frame = ctk.CTkFrame(licenses_tab)
        list_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        ctk.CTkLabel(list_frame, text=UI_TEXT['registered_licenses'], 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 20))
        
        # License display
        self.license_display = ctk.CTkTextbox(list_frame, height=UI_DIMENSIONS['license_display_height'])
        self.license_display.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(list_frame)
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.refresh_licenses_button = ctk.CTkButton(
            buttons_frame,
            text=UI_TEXT['refresh_list_btn'],
            command=self.refresh_license_list
        )
        self.refresh_licenses_button.pack(side="left", padx=10)
    
    def refresh_license_list(self):
        """Refresh the license list display"""
        self.main_app.refresh_license_list()
    
    def update_display(self, text):
        """Update license display text"""
        self.license_display.delete("0.0", "end")
        self.license_display.insert("0.0", text)