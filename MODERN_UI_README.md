# 🎮 Modern UI for MTYB Steam Manifest Tool

## ✨ What's New

The Steam Manifest Tool now features a completely redesigned modern interface with improved usability and hidden admin features.

## 🚀 How to Run

### Modern UI (Recommended)
```bash
cd src && python client_tool_gui_modern.py
```

### Classic UI (Still Available)
```bash
cd src && python client_tool_gui.py
```

## 🎨 Modern Design Features

### 🏠 Dashboard View
- **Card-based layout** with clean, modern design
- **Quick actions** for common tasks
- **System status** indicators
- **Recent activity** log
- **Welcome section** with authentication status

### 🔐 Hidden Admin Panel
- **Secret access**: Press `Ctrl+Shift+A` to unlock admin features
- **Password protection**: Default password is `admin123` (change in code)
- **System tools** for advanced users:
  - 🛡️ Windows Defender exclusion management
  - 🔧 KoaLoader integration
  - 🟢 GreenLuma DLC management
  - ⚙️ MiniTool system components
- **Advanced settings** with debug mode and auto-update
- **System information** display
- **Danger zone** for destructive operations

### 🎯 Improved User Experience
- **Modern color scheme** with proper contrast
- **Responsive design** that adapts to window size
- **Better typography** with clear hierarchy
- **Visual feedback** for all actions
- **Keyboard shortcuts** for power users
- **Status indicators** with color coding

## 🔑 Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+Shift+A` | Toggle admin access |
| `Ctrl+Shift+D` | Go to dashboard |
| `Escape` | Close admin panel |

## 🛡️ Admin Features (Hidden by Default)

### Why Hidden?
Admin features are hidden to:
- **Prevent accidental damage** from system-level operations
- **Simplify the interface** for regular users
- **Maintain security** by requiring explicit access
- **Reduce clutter** in the main interface

### Admin Tools Available:
1. **Windows Defender Exclusion**: Automatically add Steam folder to exclusions
2. **KoaLoader Management**: Check and install KoaLoader components
3. **GreenLuma Integration**: Manage DLC unlocking system
4. **MiniTool Components**: Install system integration tools
5. **Debug Mode**: Enable detailed logging for troubleshooting
6. **Auto-Update**: Configure automatic manifest updates
7. **System Information**: View detailed system and application status
8. **Data Management**: Clear data and reset settings (danger zone)

## 🎨 Design Philosophy

### Modern & Clean
- **Card-based layout** for better organization
- **Consistent spacing** and padding
- **Rounded corners** for softer appearance
- **Subtle shadows** for depth

### User-Friendly
- **Clear visual hierarchy** with proper typography
- **Intuitive navigation** with sidebar
- **Contextual information** and helpful hints
- **Progressive disclosure** of advanced features

### Responsive
- **Adaptive layout** that works on different screen sizes
- **Scalable components** that maintain proportions
- **Flexible content areas** that expand as needed

## 🔧 Technical Implementation

### File Structure
```
src/gui/
├── modern_main_window.py      # Main modern window class
├── views/
│   ├── dashboard_view.py      # Dashboard implementation
│   ├── admin_view.py          # Hidden admin panel
│   └── __init__.py
├── config/
│   └── ui_constants.py        # Updated with modern design tokens
└── components/                # Existing components (theme, tray)
```

### Key Features
- **Modular design** with separate view classes
- **Modern color palette** with CSS-like color system
- **Flexible dimensions** system for responsive design
- **Animation settings** for future enhancements
- **Comprehensive theming** support

## 🔄 Migration from Classic UI

The classic UI remains fully functional. Users can:
- **Continue using** the classic interface
- **Gradually migrate** to the modern interface
- **Switch between** interfaces as needed

## 🛠️ Customization

### Changing Admin Password
Edit `src/gui/modern_main_window.py`, line ~275:
```python
if password == "admin123":  # Change this password
```

### Modifying Colors
Edit `src/gui/config/ui_constants.py` in the `UI_COLORS` section:
```python
UI_COLORS = {
    'primary': '#2563eb',      # Change primary color
    'success': '#10b981',      # Change success color
    # ... etc
}
```

### Adding New Admin Tools
1. Add the tool function to `src/gui_backend/system_tools.py`
2. Add the button to `src/gui/views/admin_view.py` in `setup_system_tools()`
3. Implement the command handler

## 🚀 Future Enhancements

- **Animations** and smooth transitions
- **More themes** (dark, light, custom)
- **Plugin system** for extending functionality
- **Advanced dashboard** with charts and graphs
- **Notification system** with toast messages
- **Backup and restore** functionality

## 🐛 Troubleshooting

### Admin Panel Not Showing
- Make sure you're pressing `Ctrl+Shift+A` correctly
- Check that the password is correct (default: `admin123`)
- Verify the modern UI is running (`client_tool_gui_modern.py`)

### UI Elements Not Loading
- Ensure all dependencies are installed
- Check that the `views` directory exists
- Verify import paths are correct

### Performance Issues
- Close unused tabs/views
- Disable debug mode if enabled
- Check system resources

## 📝 Notes

- The modern UI is fully compatible with existing backend systems
- All original functionality is preserved
- Admin features are the same, just better organized
- The interface automatically adapts to system theme preferences
