import customtkinter as ctk
from tkinter import messagebox
import threading
from ..config.ui_constants import UI_TEXT, UI_DIMENSIONS


class AuthTab:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.setup_tab()
    
    def setup_tab(self):
        """Setup authentication tab"""
        auth_tab = self.parent.add(UI_TEXT['auth_tab'])
        
        # Authentication frame
        auth_frame = ctk.CTkFrame(auth_tab)
        auth_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # License key input
        ctk.CTkLabel(auth_frame, text=UI_TEXT['license_key_label'], 
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(20, 5))
        
        self.license_entry = ctk.CTkEntry(
            auth_frame, 
            placeholder_text=UI_TEXT['license_placeholder'],
            width=UI_DIMENSIONS['license_entry_width'],
            height=UI_DIMENSIONS['license_entry_height']
        )
        self.license_entry.pack(pady=(0, 20))
        
        # Auth button
        self.auth_button = ctk.CTkButton(
            auth_frame,
            text=UI_TEXT['authenticate_btn'],
            width=UI_DIMENSIONS['auth_button_width'],
            height=UI_DIMENSIONS['auth_button_height'],
            command=self.authenticate_license
        )
        self.auth_button.pack(pady=10)
        
        # Status display
        self.auth_status = ctk.CTkTextbox(auth_frame, 
                                         height=UI_DIMENSIONS['auth_status_height'], 
                                         width=UI_DIMENSIONS['auth_status_width'])
        self.auth_status.pack(fill="both", expand=True, pady=20)
        self.auth_status.insert("0.0", UI_TEXT['auth_welcome'])
    
    def authenticate_license(self):
        """Authenticate the entered license key"""
        license_key = self.license_entry.get().strip()
        if not license_key:
            messagebox.showerror("Error", "Please enter a license key")
            return
        
        self.auth_button.configure(state="disabled", text=UI_TEXT['authenticating_btn'])
        
        # Run authentication in background thread
        threading.Thread(
            target=self.main_app._authenticate_background, 
            args=(license_key,), 
            daemon=True
        ).start()
    
    def update_status(self, message):
        """Update authentication status display"""
        self.auth_status.insert("end", f"{message}\n")
        self.auth_status.see("end")
    
    def set_authenticated(self):
        """Update UI after successful authentication"""
        self.auth_button.configure(state="normal", text=UI_TEXT['authenticated_btn'])
    
    def set_auth_failed(self):
        """Update UI after failed authentication"""
        self.auth_button.configure(state="normal", text=UI_TEXT['authenticate_btn'])