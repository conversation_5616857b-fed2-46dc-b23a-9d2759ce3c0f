import os
from ...gui_backend.steam_operations import getSteamPath

# Initialize Steam path and related constants
try:
    STEAM_PATH = getSteamPath()
except:
    STEAM_PATH = None
    print("[MTYB] Warning: Could not detect Steam path")

# File paths
DATABASE_FILE = os.path.join(STEAM_PATH, 'app.log') if STEAM_PATH else 'app.log'
KEY_FILE = os.path.join(STEAM_PATH, 'app.info.log') if STEAM_PATH else 'app.info.log'
LOCK_FILE = os.path.join(STEAM_PATH, 'monitor.lock') if STEAM_PATH else 'monitor.lock'

# Server configuration
DEFAULT_SERVER_URL = "https://manifest.online-mtyb.com"

# Application settings
APP_NAME = "MTYB Steam Manifest Tool"
APP_VERSION = "1.0"