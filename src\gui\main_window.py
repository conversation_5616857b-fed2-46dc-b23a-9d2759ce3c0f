import customtkinter as ctk
import threading
import webbrowser
from datetime import datetime

from .config.app_settings import APP_NAME, STEAM_PATH, DATABASE_FILE, KEY_FILE, LOCK_FILE
from .config.ui_constants import UI_TEXT, UI_DIMENSIONS
from .components.theme_manager import ThemeManager
from .components.system_tray import SystemTray
from .tabs.auth_tab import AuthTab
from .tabs.manifest_tab import ManifestTab
from .tabs.license_tab import LicenseTab
from .tabs.logs_tab import LogsTab
from .tabs.settings_tab import SettingsTab

from ...gui_backend.encryption import initialize_encryption
from ...gui_backend.database import checkDatabase, read_all_license, read_license, read_all_app_id
from ...gui_backend.auth_service import initialize_keyauth, authenticate_license
from ...gui_backend.manifest_api import process_manifest, update_manifest, get_manifest
from ...gui_backend.steam_operations import remove_lock, clearCache
from ...gui_backend.system_tools import addDefenderExclusion, checkKoa<PERSON>oader, checkGreenLuma, checkMiniTool


class SteamManifestGUI:
    def __init__(self):
        # Initialize CustomTkinter
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        self.root = ctk.CTk()
        self.root.title(APP_NAME)
        self.root.geometry(f"{UI_DIMENSIONS['window_width']}x{UI_DIMENSIONS['window_height']}")
        self.root.minsize(UI_DIMENSIONS['min_width'], UI_DIMENSIONS['min_height'])
        
        # Apply system theme
        ThemeManager.apply_theme("system")
        
        # Initialize application state
        self.authenticated = False
        self.current_license = None
        self.keyauthapp = None
        self.server_url = None
        self.encryption_key = None
        
        # Initialize backend services
        self._initialize_backend()
        
        # Setup UI
        self.setup_ui()
        
        # Initialize app
        self.initialize_app()
        
        # Setup system tray
        self.system_tray = SystemTray(self)
        try:
            self.system_tray.setup_system_tray()
        except:
            pass
    
    def _initialize_backend(self):
        """Initialize backend services"""
        try:
            # Initialize encryption
            self.encryption_key = initialize_encryption(KEY_FILE)
            
            # Initialize KeyAuth
            self.keyauthapp = initialize_keyauth()
            self.server_url = self.keyauthapp.var("SERVER_API") or "https://manifest.online-mtyb.com"
            
        except Exception as e:
            self.log_message(f"[MTYB] Backend initialization error: {str(e)}")
    
    def setup_ui(self):
        """Setup the main UI components"""
        # Create main container with padding
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text=APP_NAME, 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Create tabview
        self.tabview = ctk.CTkTabview(main_frame, 
                                     width=UI_DIMENSIONS['tabview_width'], 
                                     height=UI_DIMENSIONS['tabview_height'])
        self.tabview.pack(fill="both", expand=True)
        
        # Initialize tabs
        self.auth_tab = AuthTab(self.tabview, self)
        self.manifest_tab = ManifestTab(self.tabview, self)
        self.license_tab = LicenseTab(self.tabview, self)
        self.logs_tab = LogsTab(self.tabview, self)
        self.settings_tab = SettingsTab(self.tabview, self)
        
        # Status bar
        self.status_frame = ctk.CTkFrame(main_frame)
        self.status_frame.pack(fill="x", pady=(10, 0))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame, 
            text=UI_TEXT['status_not_auth'], 
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # Theme toggle button
        self.theme_button = ctk.CTkButton(
            self.status_frame,
            text=UI_TEXT['toggle_theme_btn'],
            width=UI_DIMENSIONS['theme_button_width'],
            command=self.toggle_theme
        )
        self.theme_button.pack(side="right", padx=10, pady=5)
    
    def initialize_app(self):
        """Initialize the application"""
        self.log_message("[MTYB] Tools Initializing...")
        
        # Run initialization in background thread
        threading.Thread(target=self._background_init, daemon=True).start()
    
    def _background_init(self):
        """Background initialization tasks"""
        try:
            if STEAM_PATH:
                addDefenderExclusion(STEAM_PATH)
            checkDatabase(DATABASE_FILE)
            self.log_message("[MTYB] Initialization complete")
        except Exception as e:
            self.log_message(f"[MTYB] Initialization error: {str(e)}")
    
    def log_message(self, message):
        """Add message to log display"""
        def update_log():
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}"
            
            # Update logs tab
            self.logs_tab.add_log_message(log_entry)
            
            # Also update auth status if available
            if hasattr(self.auth_tab, 'auth_status'):
                self.auth_tab.update_status(log_entry)
        
        # Ensure this runs on the main thread
        self.root.after(0, update_log)
    
    def _authenticate_background(self, license_key):
        """Background authentication process"""
        try:
            self.log_message(f"[MTYB] Authenticating license: {license_key[:8]}...")
            
            authenticated, app_info = authenticate_license(
                license_key, self.keyauthapp, DATABASE_FILE, self.encryption_key
            )
            
            if authenticated and app_info:
                self.authenticated = True
                self.current_license = app_info
                self.log_message("[MTYB] Authentication successful!")
                
                # Enable UI elements
                self.root.after(0, self._auth_success)
                
                # Run post-auth setup
                self._post_auth_setup()
            else:
                self.log_message("[MTYB] Authentication failed.")
                self._auth_failed()
                
        except Exception as e:
            self.log_message(f"[MTYB] Authentication error: {str(e)}")
            self._auth_failed()
    
    def _auth_success(self):
        """Update UI after successful authentication"""
        self.auth_tab.set_authenticated()
        self.status_label.configure(text=UI_TEXT['status_auth'])
        
        # Enable manifest management
        self.manifest_tab.enable_controls()
        
        # Switch to manifest tab
        self.tabview.set(UI_TEXT['manifest_tab'])
        
        # Refresh license list
        self.refresh_license_list()
    
    def _auth_failed(self):
        """Update UI after failed authentication"""
        self.root.after(0, self.auth_tab.set_auth_failed)
    
    def _post_auth_setup(self):
        """Run post-authentication setup tasks"""
        try:
            if STEAM_PATH:
                remove_lock(LOCK_FILE)
                clearCache()
                checkKoaLoader(self.keyauthapp)
                checkGreenLuma(self.keyauthapp, DATABASE_FILE, self.encryption_key)
                checkMiniTool(self.keyauthapp)
            self.log_message("[MTYB] Setup complete")
        except Exception as e:
            self.log_message(f"[MTYB] Setup error: {str(e)}")
    
    def _download_manifest_background(self, app_id):
        """Background manifest download"""
        try:
            self.log_message(f"[MTYB] Starting download for App ID: {app_id}")
            self.root.after(0, lambda: self.manifest_tab.update_progress(0.2, UI_TEXT['downloading']))
            
            process_manifest(app_id, STEAM_PATH, self.server_url)
            
            self.root.after(0, lambda: self.manifest_tab.update_progress(1.0, UI_TEXT['download_complete']))
            self.log_message(f"[MTYB] Download complete for App ID: {app_id}")
            
            # Update manifest info
            self._update_manifest_info(app_id)
            
        except Exception as e:
            self.log_message(f"[MTYB] Download error: {str(e)}")
            self.root.after(0, lambda: self.manifest_tab.update_progress(0, UI_TEXT['download_failed']))
    
    def _update_all_background(self):
        """Background update all manifests"""
        try:
            app_ids = read_all_app_id(DATABASE_FILE, self.encryption_key)
            if not app_ids:
                self.log_message("[MTYB] No licenses found")
                return
            
            total = len(app_ids)
            for i, app_id in enumerate(app_ids):
                progress = (i + 1) / total
                self.root.after(0, lambda p=progress, a=app_id: self.manifest_tab.update_progress(p, f"Updating {a}..."))
                
                self.log_message(f"[MTYB] Updating App ID: {app_id}")
                update_manifest(app_id, STEAM_PATH, self.server_url)
            
            self.root.after(0, lambda: self.manifest_tab.update_progress(1.0, "All updates complete!"))
            self.log_message("[MTYB] All manifests updated")
            
        except Exception as e:
            self.log_message(f"[MTYB] Update error: {str(e)}")
    
    def launch_steam(self):
        """Launch Steam with current app"""
        app_id = self.manifest_tab.app_id_entry.get().strip()
        if not app_id:
            # Try to get from current license
            if self.authenticated and self.current_license:
                app_id = self.current_license.get('app_id', '')
            if not app_id:
                from tkinter import messagebox
                messagebox.showerror("Error", "Please enter an App ID")
                return
        
        try:
            webbrowser.open(f"steam://install/{app_id}")
            self.log_message(f"[MTYB] Launched Steam for App ID: {app_id}")
        except Exception as e:
            self.log_message(f"[MTYB] Launch error: {str(e)}")
    
    def refresh_license_list(self):
        """Refresh the license list display"""
        try:
            licenses = read_all_license(DATABASE_FILE, self.encryption_key)
            if licenses:
                display_text = "Registered Licenses:\\n\\n"
                for i, lic in enumerate(licenses, 1):
                    display_text += f"{i}. License: {lic['license_key'][:8]}...\\n"
                    display_text += f"   App ID: {lic['app_id']}\\n"
                    display_text += f"   Created: {lic['created_date']}\\n\\n"
            else:
                display_text = "No licenses registered."
            
            self.license_tab.update_display(display_text)
        except Exception as e:
            self.log_message(f"[MTYB] Error refreshing licenses: {str(e)}")
    
    def _update_manifest_info(self, app_id):
        """Update manifest information display"""
        try:
            manifest_data = get_manifest(app_id, self.server_url)
            if manifest_data:
                info_text = f"Manifest Information for App ID: {app_id}\\n\\n"
                info_text += f"Available Manifests: {len(manifest_data.get('manifests', []))}\\n\\n"
                
                for manifest in manifest_data.get('manifests', []):
                    info_text += f"- {manifest}\\n"
                
                self.root.after(0, lambda: self.manifest_tab.set_manifest_info(info_text))
        except Exception as e:
            self.log_message(f"[MTYB] Error updating manifest info: {str(e)}")
    
    def toggle_theme(self):
        """Toggle between light and dark themes"""
        current = ctk.get_appearance_mode()
        new_theme = "Light" if current == "Dark" else "Dark"
        ctk.set_appearance_mode(new_theme)
        self.log_message(f"[MTYB] Switched to {new_theme} theme")
    
    def run(self):
        """Start the GUI application"""
        self.root.mainloop()


def main_gui():
    """Main entry point for GUI version"""
    app = SteamManifestGUI()
    app.run()


if __name__ == '__main__':
    main_gui()