import customtkinter as ctk
from tkinter import messagebox
import threading
import subprocess
import os
from ..config.ui_constants import UI_TEXT, UI_DIMENSIONS, UI_COLORS
from gui_backend.system_tools import addDefenderExclusion, checkKoaLoader, checkGreenLuma, checkMiniTool
from gui_backend.steam_operations import getSteamPath


class AdminView:
    def __init__(self, parent_frame, main_app):
        self.parent_frame = parent_frame
        self.main_app = main_app
        self.setup_admin_panel()
    
    def setup_admin_panel(self):
        """Setup admin panel with system tools"""
        # Admin container with warning styling
        admin_container = ctk.CTkScrollableFrame(
            self.parent_frame,
            fg_color="transparent"
        )
        admin_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Warning header
        self.setup_warning_header(admin_container)
        
        # System tools section
        self.setup_system_tools(admin_container)
        
        # Advanced settings section
        self.setup_advanced_settings(admin_container)
        
        # System information section
        self.setup_system_info(admin_container)
        
        # Danger zone
        self.setup_danger_zone(admin_container)
    
    def setup_warning_header(self, parent):
        """Setup warning header for admin panel"""
        warning_frame = ctk.CTkFrame(
            parent,
            height=100,
            corner_radius=UI_DIMENSIONS['card_corner_radius'],
            fg_color=UI_COLORS['warning_light'],
            border_color=UI_COLORS['warning'],
            border_width=2
        )
        warning_frame.pack(fill="x", pady=(0, 20))
        warning_frame.pack_propagate(False)
        
        # Warning icon and text
        warning_label = ctk.CTkLabel(
            warning_frame,
            text="⚠️ " + UI_TEXT['admin_warning'],
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=UI_COLORS['warning']
        )
        warning_label.pack(pady=(15, 5))
        
        desc_label = ctk.CTkLabel(
            warning_frame,
            text=UI_TEXT['system_tools_desc'],
            font=ctk.CTkFont(size=12),
            text_color=UI_COLORS['text_secondary']
        )
        desc_label.pack()
    
    def setup_system_tools(self, parent):
        """Setup system tools section"""
        tools_frame = ctk.CTkFrame(
            parent,
            corner_radius=UI_DIMENSIONS['card_corner_radius']
        )
        tools_frame.pack(fill="x", pady=(0, 20))
        
        # Title
        title_label = ctk.CTkLabel(
            tools_frame,
            text="🛠️ System Tools",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 15))
        
        # Tools grid
        tools_grid = ctk.CTkFrame(tools_frame, fg_color="transparent")
        tools_grid.pack(padx=20, pady=(0, 20))
        
        # System tool buttons
        tools = [
            ("🛡️ Add Defender Exclusion", self.add_defender_exclusion, 
             "Add Windows Defender exclusion for Steam folder", UI_COLORS['info']),
            ("🔧 Check KoaLoader", self.check_koaloader, 
             "Check and install KoaLoader components", UI_COLORS['primary']),
            ("🟢 Check GreenLuma", self.check_greenluma, 
             "Check and configure GreenLuma DLC manager", UI_COLORS['success']),
            ("⚙️ Check MiniTool", self.check_minitool, 
             "Check and install MiniTool components", UI_COLORS['secondary'])
        ]
        
        for i, (text, command, desc, color) in enumerate(tools):
            row = i // 2
            col = i % 2
            
            # Tool card
            tool_card = ctk.CTkFrame(
                tools_grid,
                width=UI_DIMENSIONS['dashboard_card_width'],
                height=120
            )
            tool_card.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
            tool_card.pack_propagate(False)
            
            # Tool button
            tool_btn = ctk.CTkButton(
                tool_card,
                text=text,
                width=UI_DIMENSIONS['admin_button_width'],
                height=UI_DIMENSIONS['admin_button_height'],
                command=command,
                fg_color=color,
                hover_color=self.darken_color(color),
                font=ctk.CTkFont(size=12, weight="bold")
            )
            tool_btn.pack(pady=(15, 5))
            
            # Description
            desc_label = ctk.CTkLabel(
                tool_card,
                text=desc,
                font=ctk.CTkFont(size=10),
                text_color=UI_COLORS['text_muted'],
                wraplength=UI_DIMENSIONS['dashboard_card_width'] - 20
            )
            desc_label.pack(padx=10)
        
        # Configure grid weights
        tools_grid.grid_columnconfigure(0, weight=1)
        tools_grid.grid_columnconfigure(1, weight=1)
    
    def setup_advanced_settings(self, parent):
        """Setup advanced settings section"""
        settings_frame = ctk.CTkFrame(
            parent,
            corner_radius=UI_DIMENSIONS['card_corner_radius']
        )
        settings_frame.pack(fill="x", pady=(0, 20))
        
        # Title
        title_label = ctk.CTkLabel(
            settings_frame,
            text="⚙️ Advanced Settings",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 15))
        
        # Settings content
        settings_content = ctk.CTkFrame(settings_frame, fg_color="transparent")
        settings_content.pack(fill="x", padx=20, pady=(0, 20))
        
        # Debug mode toggle
        debug_frame = ctk.CTkFrame(settings_content)
        debug_frame.pack(fill="x", pady=5)
        
        debug_label = ctk.CTkLabel(
            debug_frame,
            text="Debug Mode:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        debug_label.pack(side="left", padx=20, pady=10)
        
        self.debug_switch = ctk.CTkSwitch(
            debug_frame,
            text="Enable detailed logging",
            command=self.toggle_debug_mode
        )
        self.debug_switch.pack(side="right", padx=20, pady=10)
        
        # Auto-update toggle
        update_frame = ctk.CTkFrame(settings_content)
        update_frame.pack(fill="x", pady=5)
        
        update_label = ctk.CTkLabel(
            update_frame,
            text="Auto-Update:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        update_label.pack(side="left", padx=20, pady=10)
        
        self.auto_update_switch = ctk.CTkSwitch(
            update_frame,
            text="Automatically update manifests",
            command=self.toggle_auto_update
        )
        self.auto_update_switch.pack(side="right", padx=20, pady=10)
    
    def setup_system_info(self, parent):
        """Setup system information display"""
        info_frame = ctk.CTkFrame(
            parent,
            corner_radius=UI_DIMENSIONS['card_corner_radius']
        )
        info_frame.pack(fill="x", pady=(0, 20))
        
        # Title
        title_label = ctk.CTkLabel(
            info_frame,
            text="💻 System Information",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 15))
        
        # System info display
        self.system_info_text = ctk.CTkTextbox(
            info_frame,
            height=150,
            font=ctk.CTkFont(size=11, family="Consolas")
        )
        self.system_info_text.pack(fill="x", padx=20, pady=(0, 20))
        
        # Populate system info
        self.update_system_info()
        
        # Refresh button
        refresh_btn = ctk.CTkButton(
            info_frame,
            text="🔄 Refresh System Info",
            width=150,
            command=self.update_system_info
        )
        refresh_btn.pack(pady=(0, 15))
    
    def setup_danger_zone(self, parent):
        """Setup danger zone with destructive actions"""
        danger_frame = ctk.CTkFrame(
            parent,
            corner_radius=UI_DIMENSIONS['card_corner_radius'],
            fg_color=UI_COLORS['error_light'],
            border_color=UI_COLORS['error'],
            border_width=2
        )
        danger_frame.pack(fill="x", pady=(0, 20))
        
        # Title
        title_label = ctk.CTkLabel(
            danger_frame,
            text="⚠️ Danger Zone",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=UI_COLORS['error']
        )
        title_label.pack(pady=(20, 10))
        
        # Warning text
        warning_text = ctk.CTkLabel(
            danger_frame,
            text="These actions cannot be undone. Use with extreme caution.",
            font=ctk.CTkFont(size=12),
            text_color=UI_COLORS['text_secondary']
        )
        warning_text.pack(pady=(0, 15))
        
        # Danger actions
        actions_frame = ctk.CTkFrame(danger_frame, fg_color="transparent")
        actions_frame.pack(padx=20, pady=(0, 20))
        
        # Clear all data button
        clear_btn = ctk.CTkButton(
            actions_frame,
            text="🗑️ Clear All Data",
            width=150,
            fg_color=UI_COLORS['error'],
            hover_color=self.darken_color(UI_COLORS['error']),
            command=self.clear_all_data
        )
        clear_btn.pack(side="left", padx=10)
        
        # Reset settings button
        reset_btn = ctk.CTkButton(
            actions_frame,
            text="🔄 Reset Settings",
            width=150,
            fg_color=UI_COLORS['warning'],
            hover_color=self.darken_color(UI_COLORS['warning']),
            command=self.reset_settings
        )
        reset_btn.pack(side="left", padx=10)
    
    # System tool implementations
    def add_defender_exclusion(self):
        """Add Windows Defender exclusion"""
        try:
            steam_path = getSteamPath()
            if steam_path:
                addDefenderExclusion(steam_path)
                messagebox.showinfo("Success", "Windows Defender exclusion added successfully!")
                self.main_app.log_message("[ADMIN] Defender exclusion added")
            else:
                messagebox.showerror("Error", "Steam path not detected")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add exclusion: {str(e)}")
    
    def check_koaloader(self):
        """Check and install KoaLoader"""
        try:
            threading.Thread(
                target=lambda: checkKoaLoader(self.main_app.keyauthapp),
                daemon=True
            ).start()
            messagebox.showinfo("Info", "KoaLoader check started in background")
            self.main_app.log_message("[ADMIN] KoaLoader check initiated")
        except Exception as e:
            messagebox.showerror("Error", f"KoaLoader check failed: {str(e)}")
    
    def check_greenluma(self):
        """Check and configure GreenLuma"""
        try:
            threading.Thread(
                target=lambda: checkGreenLuma(self.main_app.keyauthapp),
                daemon=True
            ).start()
            messagebox.showinfo("Info", "GreenLuma check started in background")
            self.main_app.log_message("[ADMIN] GreenLuma check initiated")
        except Exception as e:
            messagebox.showerror("Error", f"GreenLuma check failed: {str(e)}")
    
    def check_minitool(self):
        """Check and install MiniTool"""
        try:
            threading.Thread(
                target=lambda: checkMiniTool(self.main_app.keyauthapp),
                daemon=True
            ).start()
            messagebox.showinfo("Info", "MiniTool check started in background")
            self.main_app.log_message("[ADMIN] MiniTool check initiated")
        except Exception as e:
            messagebox.showerror("Error", f"MiniTool check failed: {str(e)}")
    
    def toggle_debug_mode(self):
        """Toggle debug mode"""
        enabled = self.debug_switch.get()
        self.main_app.log_message(f"[ADMIN] Debug mode {'enabled' if enabled else 'disabled'}")
    
    def toggle_auto_update(self):
        """Toggle auto-update mode"""
        enabled = self.auto_update_switch.get()
        self.main_app.log_message(f"[ADMIN] Auto-update {'enabled' if enabled else 'disabled'}")
    
    def update_system_info(self):
        """Update system information display"""
        try:
            import platform
            import psutil
            
            info_text = f"""System Information:
OS: {platform.system()} {platform.release()}
Architecture: {platform.architecture()[0]}
Processor: {platform.processor()}
Python: {platform.python_version()}

Memory Usage: {psutil.virtual_memory().percent}%
Disk Usage: {psutil.disk_usage('/').percent}%

Steam Path: {getSteamPath() or 'Not detected'}
Admin Mode: Active
Debug Mode: {'On' if hasattr(self, 'debug_switch') and self.debug_switch.get() else 'Off'}
"""
            
            self.system_info_text.delete("0.0", "end")
            self.system_info_text.insert("0.0", info_text)
        except Exception as e:
            self.system_info_text.delete("0.0", "end")
            self.system_info_text.insert("0.0", f"Error loading system info: {str(e)}")
    
    def clear_all_data(self):
        """Clear all application data (dangerous)"""
        result = messagebox.askyesno(
            "Confirm Deletion",
            "This will delete ALL application data including licenses and settings.\n\nAre you absolutely sure?",
            icon="warning"
        )
        if result:
            # Implement data clearing logic
            self.main_app.log_message("[ADMIN] All data cleared")
            messagebox.showinfo("Complete", "All data has been cleared")
    
    def reset_settings(self):
        """Reset all settings to default"""
        result = messagebox.askyesno(
            "Reset Settings",
            "This will reset all settings to their default values.\n\nContinue?",
            icon="question"
        )
        if result:
            # Implement settings reset logic
            self.main_app.log_message("[ADMIN] Settings reset to defaults")
            messagebox.showinfo("Complete", "Settings have been reset")
    
    def darken_color(self, color):
        """Darken a color for hover effects"""
        color_map = {
            UI_COLORS['info']: '#2563eb',
            UI_COLORS['primary']: '#1d4ed8',
            UI_COLORS['success']: '#059669',
            UI_COLORS['secondary']: '#475569',
            UI_COLORS['error']: '#dc2626',
            UI_COLORS['warning']: '#d97706'
        }
        return color_map.get(color, color)
