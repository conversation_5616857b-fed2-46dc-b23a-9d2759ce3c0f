import os
import time
from pathlib import Path

try:
    import winreg
except ImportError:
    winreg = None


def getSteamPath():
    """Get Steam installation path from Windows registry"""
    if not winreg:
        raise RuntimeError("Windows registry access not available")
    
    reg_key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Valve\\Steam")
    steam_path = winreg.QueryValueEx(reg_key, "SteamPath")[0]
    winreg.CloseKey(reg_key)

    if steam_path is None:
        print("[MTYB] Steam is not installed.")
        time.sleep(5)
        exit(0)

    return steam_path


def ensure_dir_exists(path):
    """Create directory if it doesn't exist"""
    if not os.path.exists(path):
        os.makedirs(path)


def clearCache():
    """Clear Steam cache files"""
    steam_path = getSteamPath()
    appinfo_path = os.path.join(steam_path, 'appcache', 'appinfo.vdf')
    packageinfo_path = os.path.join(steam_path, 'appcache', 'packageinfo.vdf')

    # Check if files exist and delete them
    if os.path.exists(appinfo_path):
        os.remove(appinfo_path)

    if os.path.exists(packageinfo_path):
        os.remove(packageinfo_path)


def remove_lock(lock_file):
    """Remove monitor lock file"""
    if os.path.exists(lock_file):
        os.remove(lock_file)